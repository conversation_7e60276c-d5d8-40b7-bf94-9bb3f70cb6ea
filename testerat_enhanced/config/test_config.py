"""
Universal Test Configuration for testerat Enhanced

Supports any web application framework and deployment environment.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from enum import Enum


class AuthType(Enum):
    """Universal authentication types"""
    AUTO_DETECT = "auto_detect"  # Auto-detect authentication method
    FORM_BASED = "form_based"  # Traditional username/password forms
    OAUTH = "oauth"  # OAuth providers (Google, GitHub, etc.)
    JWT = "jwt"  # JWT token-based auth
    SESSION = "session"  # Session-based auth
    NEXTAUTH = "nextauth"  # NextAuth.js
    AUTH0 = "auth0"  # Auth0
    CUSTOM = "custom"  # Custom authentication system


class FrameworkType(Enum):
    """Supported web frameworks"""
    REACT = "react"
    VUE = "vue"
    ANGULAR = "angular"
    VANILLA_JS = "vanilla_js"
    NEXTJS = "nextjs"
    NUXTJS = "nuxtjs"
    SVELTE = "svelte"
    AUTO_DETECT = "auto_detect"


@dataclass
class AuthConfig:
    """Universal authentication configuration"""
    auth_type: AuthType = AuthType.AUTO_DETECT
    login_url: str = "/login"
    logout_url: str = "/logout"
    protected_routes: List[str] = field(default_factory=lambda: ["/dashboard", "/profile"])
    
    # Test user credentials
    test_users: Dict[str, Dict[str, str]] = field(default_factory=lambda: {
        "standard": {"email": "<EMAIL>", "password": "testpassword"},
        "admin": {"email": "<EMAIL>", "password": "adminpassword"}
    })
    
    # Authentication selectors (universal CSS selectors)
    selectors: Dict[str, str] = field(default_factory=lambda: {
        "email_input": 'input[type="email"], input[name="email"], #email',
        "password_input": 'input[type="password"], input[name="password"], #password',
        "login_button": 'button[type="submit"], input[type="submit"], .login-btn',
        "logout_button": '.logout, [data-testid="logout"], button:has-text("logout")',
        "user_menu": '.user-menu, .profile-menu, [data-testid="user-menu"]',
        "auth_indicator": '.user-name, .username, [data-testid="user-indicator"]'
    })
    
    # Session validation
    session_timeout: int = 30  # minutes
    csrf_token_required: bool = True
    csrf_token_selectors: List[str] = field(default_factory=lambda: [
        'meta[name="csrf-token"]',
        'input[name="_token"]',
        'input[name="csrf_token"]'
    ])


@dataclass
class WorkflowConfig:
    """Universal workflow testing configuration"""
    step_timeout: int = 10000  # milliseconds
    navigation_timeout: int = 5000  # milliseconds
    form_submission_timeout: int = 15000  # milliseconds
    
    # Universal form selectors
    form_selectors: Dict[str, str] = field(default_factory=lambda: {
        "next_button": 'button:has-text("next"), .next-btn, [data-testid="next"]',
        "previous_button": 'button:has-text("previous"), .prev-btn, [data-testid="previous"]',
        "submit_button": 'button[type="submit"], .submit-btn, [data-testid="submit"]',
        "cancel_button": 'button:has-text("cancel"), .cancel-btn, [data-testid="cancel"]'
    })
    
    # Validation patterns
    required_field_indicators: List[str] = field(default_factory=lambda: [
        '*', 'required', 'aria-required="true"'
    ])
    error_message_selectors: List[str] = field(default_factory=lambda: [
        '.error', '.error-message', '[role="alert"]', '.invalid-feedback'
    ])


@dataclass
class APIConfig:
    """Universal API testing configuration"""
    base_url: str = ""  # Will be auto-detected from current page
    timeout: int = 30000  # milliseconds
    
    # CSRF protection
    csrf_header_variations: List[str] = field(default_factory=lambda: [
        "X-CSRF-Token",  # Rails standard
        "x-csrf-token",  # Lowercase variant
        "X-CSRF-TOKEN",  # Uppercase variant
        "csrf-token",    # Simple variant
        "X-Requested-With"  # Alternative protection
    ])
    
    # API endpoints to test
    common_endpoints: List[str] = field(default_factory=lambda: [
        "/api/auth/signin",
        "/api/auth/signout", 
        "/api/csrf-token",
        "/api/user/profile",
        "/api/forms/submit"
    ])
    
    # Expected response patterns
    success_indicators: List[str] = field(default_factory=lambda: [
        "success", "ok", "completed", "created"
    ])
    error_indicators: List[str] = field(default_factory=lambda: [
        "error", "failed", "invalid", "unauthorized", "forbidden"
    ])


@dataclass
class UniversalTestConfig:
    """Universal configuration for any web application"""
    
    # Browser configuration
    headless: bool = True
    viewport_width: int = 1920
    viewport_height: int = 1080
    timeout: int = 30000
    
    # Framework detection
    framework: FrameworkType = FrameworkType.AUTO_DETECT
    
    # Testing modules
    auth_config: AuthConfig = field(default_factory=AuthConfig)
    workflow_config: WorkflowConfig = field(default_factory=WorkflowConfig)
    api_config: APIConfig = field(default_factory=APIConfig)
    
    # Performance thresholds (universal)
    performance_thresholds: Dict[str, int] = field(default_factory=lambda: {
        'load_time': 3000,
        'first_contentful_paint': 2000,
        'largest_contentful_paint': 4000,
        'cumulative_layout_shift': 0.1
    })
    
    # Testing scope
    test_authentication: bool = True
    test_workflows: bool = True
    test_api_interactions: bool = True
    test_security: bool = True
    test_accessibility: bool = True
    test_performance: bool = True
    
    # Reporting
    generate_screenshots: bool = True
    generate_video: bool = False
    detailed_logging: bool = True
    export_formats: List[str] = field(default_factory=lambda: ["html", "json"])
    
    # Universal selectors for framework detection
    framework_indicators: Dict[FrameworkType, List[str]] = field(default_factory=lambda: {
        FrameworkType.REACT: ["[data-reactroot]", "#__next", "._app"],
        FrameworkType.VUE: ["#app", ".vue-app", "[data-app]"],
        FrameworkType.ANGULAR: ["[ng-version]", "[ng-app]", "app-root"],
        FrameworkType.NEXTJS: ["#__next", "_next/static"],
        FrameworkType.NUXTJS: ["#__nuxt", "_nuxt/"],
        FrameworkType.SVELTE: [".svelte-app", "[data-svelte]"]
    })


def create_config_for_framework(framework: FrameworkType, base_url: str = "") -> UniversalTestConfig:
    """Create optimized configuration for specific framework with comprehensive optimizations"""
    config = UniversalTestConfig()
    config.framework = framework
    config.api_config.base_url = base_url

    # Framework-specific optimizations
    if framework == FrameworkType.NEXTJS:
        # Next.js specific optimizations
        config.auth_config.auth_type = AuthType.NEXTAUTH
        config.auth_config.login_url = "/api/auth/signin"
        config.auth_config.logout_url = "/api/auth/signout"
        config.auth_config.selectors.update({
            "auth_indicator": "[data-testid='user-menu'], .user-menu, .next-auth-user",
            "login_button": "[data-testid='signin'], .signin-btn",
            "logout_button": "[data-testid='signout'], .signout-btn"
        })
        config.workflow_config.step_timeout = 8000  # Next.js hydration
        config.api_config.common_endpoints.extend([
            "/api/auth/session", "/api/auth/csrf", "/_next/static"
        ])

    elif framework == FrameworkType.REACT:
        # React specific optimizations
        config.workflow_config.step_timeout = 8000  # React state updates
        config.workflow_config.form_selectors.update({
            "react_form": "[data-reactroot] form, .react-form",
            "react_button": "[data-reactroot] button, .react-btn"
        })
        config.auth_config.selectors.update({
            "auth_indicator": "[data-testid='user'], .user-info, .auth-status",
            "loading_indicator": ".loading, .spinner, [data-testid='loading']"
        })

    elif framework == FrameworkType.VUE:
        # Vue.js specific optimizations
        config.workflow_config.step_timeout = 6000  # Vue reactivity
        config.workflow_config.form_selectors.update({
            "vue_form": "[data-app] form, .vue-form",
            "vue_button": "[data-app] button, .vue-btn"
        })
        config.auth_config.selectors.update({
            "auth_indicator": ".v-user, [data-testid='vue-user']",
            "loading_indicator": ".v-progress, .vue-loading"
        })

    elif framework == FrameworkType.ANGULAR:
        # Angular specific optimizations
        config.workflow_config.step_timeout = 10000  # Angular change detection
        config.workflow_config.form_selectors.update({
            "angular_form": "app-root form, .ng-form",
            "angular_button": "app-root button, .mat-button"
        })
        config.auth_config.selectors.update({
            "auth_indicator": ".ng-user, [data-testid='angular-user']",
            "loading_indicator": ".mat-spinner, .ng-loading"
        })
        config.api_config.common_endpoints.extend([
            "/api/auth", "/assets/"
        ])

    elif framework == FrameworkType.SVELTE:
        # Svelte specific optimizations
        config.workflow_config.step_timeout = 5000  # Svelte reactivity
        config.workflow_config.form_selectors.update({
            "svelte_form": "[data-svelte] form, .svelte-form",
            "svelte_button": "[data-svelte] button, .svelte-btn"
        })
        config.auth_config.selectors.update({
            "auth_indicator": ".svelte-user, [data-testid='svelte-user']",
            "loading_indicator": ".svelte-loading, .loading"
        })

    elif framework == FrameworkType.NUXTJS:
        # Nuxt.js specific optimizations
        config.workflow_config.step_timeout = 7000  # Nuxt SSR/hydration
        config.auth_config.login_url = "/auth/login"
        config.auth_config.logout_url = "/auth/logout"
        config.auth_config.selectors.update({
            "auth_indicator": ".nuxt-user, [data-testid='nuxt-user']",
            "loading_indicator": ".nuxt-loading, .loading-bar"
        })
        config.api_config.common_endpoints.extend([
            "/_nuxt/", "/api/auth"
        ])

    elif framework == FrameworkType.VANILLA_JS:
        # Vanilla JS optimizations (conservative approach)
        config.workflow_config.step_timeout = 5000  # Standard DOM updates
        config.auth_config.selectors.update({
            "auth_indicator": ".user, .logged-in, #user-info",
            "loading_indicator": ".loading, .spinner, #loading"
        })

    # Universal performance optimizations based on framework
    if framework in [FrameworkType.NEXTJS, FrameworkType.NUXTJS]:
        # SSR frameworks - longer initial load times
        config.performance_thresholds['load_time'] = 4000
        config.performance_thresholds['first_contentful_paint'] = 2500
    elif framework in [FrameworkType.REACT, FrameworkType.VUE, FrameworkType.ANGULAR]:
        # SPA frameworks - faster subsequent navigation
        config.performance_thresholds['load_time'] = 3000
        config.performance_thresholds['first_contentful_paint'] = 2000
    elif framework == FrameworkType.SVELTE:
        # Svelte - typically faster
        config.performance_thresholds['load_time'] = 2500
        config.performance_thresholds['first_contentful_paint'] = 1500

    return config
