#!/usr/bin/env python3
"""
Comprehensive Test for Enhanced Testerat
Tests all functionality against multiple websites to ensure everything works
"""

import sys
import os
import time
from pathlib import Path

# Add the parent directory to path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from testerat_enhanced import EnhancedTesterat, UniversalTestConfig, FrameworkType


def test_comprehensive_functionality():
    """Test all Enhanced Testerat functionality comprehensively"""
    print("🎯 Comprehensive Enhanced Testerat Testing")
    print("=" * 60)
    
    test_sites = [
        {
            "url": "https://httpbin.org/html",
            "name": "HTTPBin (Simple HTML)",
            "expected_framework": "vanilla_js",
            "enable_all": True
        },
        {
            "url": "https://example.com",
            "name": "Example.com (Basic Site)",
            "expected_framework": "vanilla_js", 
            "enable_all": True
        },
        {
            "url": "https://httpbin.org/forms/post",
            "name": "HTTPBin Forms (Form Testing)",
            "expected_framework": "vanilla_js",
            "enable_all": True
        }
    ]
    
    total_tests = 0
    passed_tests = 0
    all_results = []
    
    for site in test_sites:
        print(f"\n🌐 Testing: {site['name']}")
        print(f"   URL: {site['url']}")
        print("-" * 40)
        
        try:
            # Create comprehensive configuration
            config = UniversalTestConfig()
            config.headless = True
            config.detailed_logging = True
            
            # Enable all testing features
            config.test_authentication = True
            config.test_workflows = True
            config.test_api_interactions = True
            config.test_security = True
            config.test_accessibility = True
            config.test_performance = True
            
            # Initialize testerat
            testerat = EnhancedTesterat(config)
            
            # Run comprehensive test
            start_time = time.time()
            results = testerat.run_comprehensive_test(site['url'], f"Comprehensive Test - {site['name']}")
            end_time = time.time()
            
            # Analyze results
            summary = results['summary']
            critical_issues = results['critical_issues']
            
            print(f"   ✅ Test completed in {end_time - start_time:.1f}s")
            print(f"   📊 Tests run: {summary['total_tests']}")
            print(f"   📊 Success rate: {summary['success_rate']:.1f}%")
            print(f"   🚨 Critical issues: {len(critical_issues)}")
            
            # Check for specific functionality
            if summary['total_tests'] > 0:
                print(f"   ✅ Tests actually executed")
                passed_tests += 1
            else:
                print(f"   ❌ No tests executed")
            
            if summary['success_rate'] > 50:
                print(f"   ✅ Reasonable success rate")
                passed_tests += 1
            else:
                print(f"   ❌ Low success rate")
            
            if 'report_files' in results and results['report_files']:
                print(f"   ✅ Reports generated: {', '.join(results['report_files'])}")
                passed_tests += 1
            else:
                print(f"   ❌ No reports generated")
            
            total_tests += 3
            all_results.append(results)
            
            # Show critical issues if any
            if critical_issues:
                print(f"   🚨 Critical Issues Found:")
                for issue in critical_issues[:3]:  # Show first 3
                    print(f"      - {issue['test_name']}: {issue['details'][:100]}...")
            
        except Exception as e:
            print(f"   ❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"📊 Comprehensive Test Results:")
    print(f"   Tests Passed: {passed_tests}/{total_tests}")
    print(f"   Success Rate: {(passed_tests/total_tests*100):.1f}%")
    
    return passed_tests == total_tests, all_results


def test_specific_fixes():
    """Test that specific architectural fixes are working"""
    print("\n🔧 Testing Specific Architectural Fixes")
    print("=" * 60)
    
    try:
        # Add the parent directory to path for imports
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)
            
        from testerat_enhanced import EnhancedTesterat, UniversalTestConfig
        
        # Test 1: Framework Detection Improvements
        print("\n🔍 Testing Enhanced Framework Detection...")
        config = UniversalTestConfig()
        config.headless = True
        testerat = EnhancedTesterat(config)
        
        # Check that enhanced detection methods exist
        assert hasattr(testerat, '_detect_framework_by_dom')
        assert hasattr(testerat, '_detect_framework_by_javascript')
        assert hasattr(testerat, '_detect_framework_by_meta')
        print("   ✅ Enhanced detection methods present")
        
        # Test 2: Error Handling Improvements
        print("\n🚨 Testing Error Handling...")
        from testerat_enhanced.utils.error_handling import ErrorHandler, ErrorSeverity
        
        error_handler = ErrorHandler()
        test_error = ValueError("Test error")
        
        # Test safe execution
        result = error_handler.safe_execute(
            lambda: 1/0,  # This will fail
            context="test_division",
            default_return="safe_default"
        )
        assert result == "safe_default"
        print("   ✅ Safe execution working")
        
        # Test 3: Real Performance Testing
        print("\n⚡ Testing Performance Implementation...")
        config = UniversalTestConfig()
        config.headless = True
        config.test_performance = True
        config.test_authentication = False
        config.test_workflows = False
        config.test_api_interactions = False
        config.test_security = False
        config.test_accessibility = False
        
        testerat = EnhancedTesterat(config)
        results = testerat.run_comprehensive_test("https://httpbin.org/html", "Performance Test")
        
        # Check that performance tests actually ran
        performance_tests = [r for r in results.get('all_results', []) if 'performance' in r.get('test_name', '').lower()]
        if performance_tests:
            print("   ✅ Performance tests executed")
        else:
            print("   ⚠️  Performance tests may not have run")
        
        # Test 4: CSRF Monitoring (Real vs Fake)
        print("\n🔒 Testing CSRF Implementation...")
        config = UniversalTestConfig()
        config.headless = True
        config.test_api_interactions = True
        config.test_authentication = False
        config.test_workflows = False
        config.test_security = False
        config.test_accessibility = False
        config.test_performance = False
        
        testerat = EnhancedTesterat(config)
        results = testerat.run_comprehensive_test("https://httpbin.org/forms/post", "CSRF Test")
        
        # Check that API tests ran (which include CSRF monitoring)
        api_tests = [r for r in results.get('all_results', []) if 'api' in r.get('test_name', '').lower()]
        if api_tests:
            print("   ✅ API/CSRF tests executed")
        else:
            print("   ⚠️  API/CSRF tests may not have run")
        
        print("\n✅ All specific fixes validated!")
        return True
        
    except Exception as e:
        print(f"\n❌ Specific fixes test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_edge_cases():
    """Test edge cases and error handling"""
    print("\n🎯 Testing Edge Cases and Error Handling")
    print("=" * 60)
    
    edge_cases = [
        {
            "name": "Invalid URL",
            "url": "https://this-domain-does-not-exist-12345.com",
            "should_fail": True
        },
        {
            "name": "Localhost (No HTTPS)",
            "url": "http://localhost:3000",
            "should_fail": False  # Should handle gracefully
        },
        {
            "name": "Very Slow Site",
            "url": "https://httpbin.org/delay/5",
            "should_fail": False  # Should handle timeouts gracefully
        }
    ]
    
    passed = 0
    total = len(edge_cases)
    
    for case in edge_cases:
        print(f"\n🧪 Testing: {case['name']}")
        print(f"   URL: {case['url']}")
        
        try:
            config = UniversalTestConfig()
            config.headless = True
            config.browser_timeout = 10000  # 10 second timeout for edge cases
            config.test_authentication = False
            config.test_workflows = False
            config.test_api_interactions = True
            config.test_security = True
            config.test_accessibility = False
            config.test_performance = False
            
            testerat = EnhancedTesterat(config)
            results = testerat.run_comprehensive_test(case['url'], f"Edge Case - {case['name']}")
            
            if case['should_fail']:
                print(f"   ⚠️  Expected failure but test completed")
            else:
                print(f"   ✅ Handled gracefully")
                passed += 1
                
        except Exception as e:
            if case['should_fail']:
                print(f"   ✅ Failed as expected: {type(e).__name__}")
                passed += 1
            else:
                print(f"   ❌ Unexpected failure: {e}")
    
    print(f"\n📊 Edge Cases: {passed}/{total} handled correctly")
    return passed >= total * 0.8  # 80% success rate acceptable for edge cases


def run_comprehensive_tests():
    """Run all comprehensive tests"""
    print("🎯 Enhanced Testerat Comprehensive Validation")
    print("=" * 80)
    
    tests = [
        ("Comprehensive Functionality", test_comprehensive_functionality),
        ("Specific Architectural Fixes", test_specific_fixes),
        ("Edge Cases and Error Handling", test_edge_cases),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 80)
    print(f"🎯 FINAL COMPREHENSIVE RESULTS:")
    print(f"   Test Categories Passed: {passed}/{total}")
    print(f"   Overall Success Rate: {(passed/total*100):.1f}%")
    
    if passed == total:
        print("\n🎉 ALL COMPREHENSIVE TESTS PASSED!")
        print("Enhanced Testerat is fully functional and production-ready!")
        return True
    else:
        print(f"\n⚠️  {total - passed} test category(ies) failed.")
        print("Enhanced Testerat needs additional fixes.")
        return False


if __name__ == "__main__":
    # Change to the testerat_enhanced directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Run comprehensive tests
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
