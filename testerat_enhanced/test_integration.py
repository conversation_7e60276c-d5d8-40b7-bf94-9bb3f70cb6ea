#!/usr/bin/env python3
"""
Integration Test for Enhanced Testerat
Tests the framework end-to-end to ensure it actually works
"""

import sys
import os
import subprocess
import time
import logging
from pathlib import Path

def test_dependencies():
    """Test that all required dependencies are available"""
    print("🔍 Testing Dependencies...")
    
    required_packages = [
        'playwright',
        'requests',
        'beautifulsoup4',
        'jinja2'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies available")
    return True


def test_playwright_browsers():
    """Test that Playwright browsers are installed"""
    print("\n🌐 Testing Playwright Browsers...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            # Test Chromium
            try:
                browser = p.chromium.launch(headless=True)
                browser.close()
                print("  ✅ Chromium browser")
            except Exception as e:
                print(f"  ❌ Chromium browser - {e}")
                return False
            
            # Test Firefox
            try:
                browser = p.firefox.launch(headless=True)
                browser.close()
                print("  ✅ Firefox browser")
            except Exception as e:
                print(f"  ⚠️  Firefox browser - {e}")
            
            # Test WebKit
            try:
                browser = p.webkit.launch(headless=True)
                browser.close()
                print("  ✅ WebKit browser")
            except Exception as e:
                print(f"  ⚠️  WebKit browser - {e}")
        
        print("✅ Playwright browsers available")
        return True
        
    except Exception as e:
        print(f"❌ Playwright browser test failed: {e}")
        print("Run: playwright install")
        return False


def test_imports():
    """Test that all Enhanced Testerat modules can be imported"""
    print("\n📦 Testing Module Imports...")
    
    try:
        # Test main imports
        from testerat_enhanced import EnhancedTesterat, UniversalTestConfig, FrameworkType
        print("  ✅ Main modules")
        
        # Test engine imports
        from testerat_enhanced.engines.authentication import AuthenticationEngine
        from testerat_enhanced.engines.workflow import WorkflowEngine
        from testerat_enhanced.engines.api import APIEngine
        print("  ✅ Engine modules")
        
        # Test utility imports
        from testerat_enhanced.utils.error_handling import ErrorHandler
        from testerat_enhanced.utils.test_result import TestResult
        print("  ✅ Utility modules")
        
        # Test reporting imports
        from testerat_enhanced.reporting.enhanced_reporter import EnhancedReporter
        print("  ✅ Reporting modules")
        
        print("✅ All modules import successfully")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False


def test_basic_functionality():
    """Test basic framework functionality"""
    print("\n⚙️  Testing Basic Functionality...")
    
    try:
        from testerat_enhanced import EnhancedTesterat, UniversalTestConfig
        
        # Create configuration
        config = UniversalTestConfig()
        config.headless = True
        config.test_authentication = False  # Skip auth for basic test
        config.test_workflows = False       # Skip workflows for basic test
        config.test_api_interactions = False # Skip API for basic test
        
        # Initialize testerat
        testerat = EnhancedTesterat(config)
        print("  ✅ Framework initialization")
        
        # Test configuration
        assert hasattr(testerat, 'config')
        assert hasattr(testerat, 'logger')
        print("  ✅ Configuration setup")
        
        print("✅ Basic functionality works")
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False


def test_cli_interface():
    """Test the CLI interface"""
    print("\n💻 Testing CLI Interface...")
    
    try:
        # Test version command
        result = subprocess.run([
            sys.executable, '-m', 'testerat_enhanced.cli', '--version'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("  ✅ CLI version command")
        else:
            print(f"  ❌ CLI version command failed: {result.stderr}")
            return False
        
        # Test help command
        result = subprocess.run([
            sys.executable, '-m', 'testerat_enhanced.cli', '--help'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and 'Enhanced Testerat' in result.stdout:
            print("  ✅ CLI help command")
        else:
            print(f"  ❌ CLI help command failed")
            return False
        
        print("✅ CLI interface works")
        return True
        
    except Exception as e:
        print(f"❌ CLI test failed: {e}")
        return False


def test_real_website():
    """Test against a real website"""
    print("\n🌐 Testing Against Real Website...")
    
    try:
        from testerat_enhanced import EnhancedTesterat, UniversalTestConfig
        
        # Create minimal configuration for quick test
        config = UniversalTestConfig()
        config.headless = True
        config.test_authentication = False
        config.test_workflows = False
        config.test_api_interactions = False
        config.test_security = False
        config.test_accessibility = False
        config.test_performance = True  # Only test performance for speed
        
        # Initialize testerat
        testerat = EnhancedTesterat(config)
        
        # Test against httpbin.org (reliable test site)
        print("  🔄 Testing httpbin.org...")
        results = testerat.run_comprehensive_test("https://httpbin.org/html", "Integration Test")
        
        # Validate results structure
        assert 'summary' in results
        assert 'critical_issues' in results
        assert 'recommendations' in results
        assert 'report_files' in results
        
        print(f"  ✅ Test completed - {results['summary']['total_tests']} tests run")
        print(f"  ✅ Success rate: {results['summary']['success_rate']:.1f}%")
        
        print("✅ Real website testing works")
        return True
        
    except Exception as e:
        print(f"❌ Real website test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_integration_tests():
    """Run all integration tests"""
    print("🧪 Enhanced Testerat Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Playwright Browsers", test_playwright_browsers),
        ("Module Imports", test_imports),
        ("Basic Functionality", test_basic_functionality),
        ("CLI Interface", test_cli_interface),
        ("Real Website Testing", test_real_website),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n❌ {test_name} test failed")
        except Exception as e:
            print(f"\n❌ {test_name} test error: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Integration Test Results:")
    print(f"   Tests Passed: {passed}/{total}")
    print(f"   Success Rate: {(passed/total*100):.1f}%")
    
    if passed == total:
        print("\n🎉 ALL INTEGRATION TESTS PASSED!")
        print("Enhanced Testerat is ready for production use.")
        return True
    else:
        print(f"\n⚠️  {total - passed} integration test(s) failed.")
        print("Please fix the issues before using Enhanced Testerat.")
        return False


if __name__ == "__main__":
    # Change to the testerat_enhanced directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Run integration tests
    success = run_integration_tests()
    sys.exit(0 if success else 1)
